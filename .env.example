# Environment Configuration (Example)
NODE_ENV=production
PORT=3000

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/whatsapp-api

# WhatsApp Configuration
WHATSAPP_SESSION_PATH=./sessions
QR_CODE_TIMEOUT=60000

# WhatsApp Browser Configuration (how device appears in linked devices)
WHATSAPP_DEVICE_NAME=WhatsApp API      # Device name shown in linked devices
WHATSAPP_BROWSER=Chrome                # Browser name shown in linked devices
WHATSAPP_VERSION=4.0.0                 # Version shown in linked devices

# Session Management (Memory Optimization)
MAX_ACTIVE_SESSIONS=100          # Maximum concurrent sessions
MAX_RAM_USAGE_MB=10240          # Max RAM usage in MB (default: 10GB)
SESSION_CLEANUP_INTERVAL=300000  # Cleanup interval in ms (5 minutes)

# API Configuration
API_PREFIX=api/v1

# Health Monitoring Configuration
HEALTH_CHECK_INTERVAL=300000          # Health check interval in ms (5 minutes)
RESPONSE_TIME_THRESHOLD=5000          # Response time threshold in ms (5 seconds)
ERROR_RATE_THRESHOLD=0.1              # Error rate threshold (10%)
INACTIVITY_THRESHOLD=3600000          # Inactivity threshold in ms (1 hour)

# Enhanced Error Handling Configuration
CIRCUIT_BREAKER_THRESHOLD=5           # Number of errors before circuit breaker opens
CIRCUIT_BREAKER_TIMEOUT=300000        # Circuit breaker timeout in ms (5 minutes)
MAX_ERROR_COUNT=10                    # Maximum error count before blocking
ERROR_RESET_INTERVAL=3600000          # Error reset interval in ms (1 hour)
