# MongoDB Configuration for WhatsApp Session Persistence
# Optimized for high-performance session storage

# Network interfaces
net:
  port: 27017
  bindIp: 0.0.0.0
  maxIncomingConnections: 1000
  wireObjectCheck: true
  ipv6: false

# Storage configuration
storage:
  dbPath: /data/db
  journal:
    enabled: true
    commitIntervalMs: 100
  directoryPerDB: true
  syncPeriodSecs: 60
  engine: wiredTiger
  wiredTiger:
    engineConfig:
      cacheSizeGB: 4
      journalCompressor: snappy
      directoryForIndexes: true
    collectionConfig:
      blockCompressor: snappy
    indexConfig:
      prefixCompression: true

# System log
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log
  logRotate: reopen
  verbosity: 1
  component:
    accessControl:
      verbosity: 1
    command:
      verbosity: 1

# Process management
processManagement:
  fork: false
  pidFilePath: /var/run/mongodb/mongod.pid

# Security
security:
  authorization: enabled
  javascriptEnabled: false

# Operation profiling
operationProfiling:
  mode: slowOp
  slowOpThresholdMs: 1000
  slowOpSampleRate: 0.1

# Replication (for production resilience)
# replication:
#   replSetName: "whatsapp-sessions-rs"

# Sharding (for horizontal scaling)
# sharding:
#   clusterRole: shardsvr

# Set parameters for session persistence optimization
setParameter:
  # Increase connection pool size
  connPoolMaxConnsPerHost: 200
  connPoolMaxShardedConnsPerHost: 200
  
  # Optimize for session workload
  wiredTigerConcurrentReadTransactions: 128
  wiredTigerConcurrentWriteTransactions: 128
  
  # Increase operation timeout
  maxTimeMS: 30000
  
  # Optimize cursor timeout for long-running operations
  cursorTimeoutMillis: 600000
  
  # Increase write concern timeout
  writeConcernTimeoutMS: 30000
  
  # Optimize for frequent updates
  wiredTigerEngineRuntimeConfig: "cache_size=4GB,eviction_target=80,eviction_trigger=95"
