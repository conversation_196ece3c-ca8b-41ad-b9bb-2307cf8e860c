version: '3.8'

services:
  # WhatsApp API Application
  whatsapp-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: whatsapp-api-prod
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongodb:27017/whatsapp-api-prod
      - MAX_ACTIVE_SESSIONS=2000
      - MAX_RAM_USAGE_MB=32768
      - SESSION_IDLE_TIMEOUT_MS=0
      - ENABLE_SESSION_BACKUP=true
      - BACKUP_RETENTION_DAYS=90
      - ENABLE_HEALTH_MONITORING=true
      - NODE_ID=prod-node-01
      - DEPLOYMENT_VERSION=1.0.0
    ports:
      - "3000:3000"
    volumes:
      - ./backups:/app/backups
      - ./logs:/app/logs
    depends_on:
      - mongodb
      - redis
    networks:
      - whatsapp-network
    deploy:
      resources:
        limits:
          memory: 32G
          cpus: '8'
        reservations:
          memory: 16G
          cpus: '4'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # MongoDB for Session Storage
  mongodb:
    image: mongo:7.0
    container_name: mongodb-prod
    restart: unless-stopped
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=your-secure-password
      - MONGO_INITDB_DATABASE=whatsapp-api-prod
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
      - ./mongodb/mongod.conf:/etc/mongod.conf
      - ./mongodb/init-scripts:/docker-entrypoint-initdb.d
    command: mongod --config /etc/mongod.conf
    networks:
      - whatsapp-network
    deploy:
      resources:
        limits:
          memory: 8G
          cpus: '4'
        reservations:
          memory: 4G
          cpus: '2'

  # Redis for Caching and Session Coordination
  redis:
    image: redis:7.2-alpine
    container_name: redis-prod
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - whatsapp-network
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1'
        reservations:
          memory: 1G
          cpus: '0.5'

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - whatsapp-api
    networks:
      - whatsapp-network

  # MongoDB Backup Service
  mongodb-backup:
    image: mongo:7.0
    container_name: mongodb-backup
    restart: "no"
    environment:
      - MONGO_URI=*************************************************************************************
    volumes:
      - ./backups/mongodb:/backup
      - ./scripts/backup-mongodb.sh:/backup-mongodb.sh
    command: /bin/bash /backup-mongodb.sh
    depends_on:
      - mongodb
    networks:
      - whatsapp-network
    profiles:
      - backup

  # Monitoring with Prometheus (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus-prod
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    networks:
      - whatsapp-network
    profiles:
      - monitoring

  # Grafana for Visualization (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: grafana-prod
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=your-grafana-password
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - whatsapp-network
    profiles:
      - monitoring

volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  whatsapp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
