{"name": "whatsapp-api", "version": "1.0.0", "description": "WhatsApp API using Baileys library with MongoDB session storage", "author": "", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^10.4.19", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.19", "@nestjs/mongoose": "^10.1.0", "@nestjs/platform-express": "^10.4.19", "@whiskeysockets/baileys": "^6.7.18", "axios": "^1.6.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "mime-types": "^2.1.35", "mongoose": "^8.16.1", "pino": "^8.17.2", "qrcode": "^1.5.4", "reflect-metadata": "^0.1.14", "rxjs": "^7.8.2"}, "devDependencies": {"@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.23", "@types/jest": "^29.5.2", "@types/mime-types": "^2.1.4", "@types/node": "^20.19.4", "@types/qrcode": "^1.5.5", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "pino-pretty": "^13.0.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}