# Production Environment Configuration for WhatsApp Session Persistence
NODE_ENV=production

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/whatsapp-api-prod

# Session Persistence Configuration
# CRITICAL: These settings ensure sessions NEVER expire automatically
SESSION_IDLE_TIMEOUT_MS=0
SESSION_CLEANUP_INTERVAL=3600000
QR_REFRESH_INTERVAL_MS=0

# Resource Management - Increased for Production
MAX_ACTIVE_SESSIONS=2000
MAX_RAM_USAGE_MB=32768
EMERGENCY_CLEANUP_THRESHOLD=0.98

# Session Restoration
ENABLE_SESSION_RESTORATION=true
RESTORATION_BATCH_SIZE=20
RESTORATION_DELAY=3000
RESTORATION_RETRY_DELAY=300000
RESTORATION_MAX_RETRIES=5

# Reconnection Settings
MAX_RECONNECTION_ATTEMPTS=10
MAX_CONFLICT_ATTEMPTS=3
RECONNECTION_BASE_DELAY=5000
RECONNECTION_MAX_DELAY=600000

# Circuit Breaker Settings
CIRCUIT_BREAKER_THRESHOLD=8
CIRCUIT_BREAKER_TIMEOUT=600000
MAX_ERROR_COUNT=15

# Health Monitoring
ENABLE_HEALTH_MONITORING=true
HEALTH_CHECK_INTERVAL=60000
ENABLE_METRICS=true

# Session Backup
ENABLE_SESSION_BACKUP=true
BACKUP_INTERVAL=86400000
BACKUP_RETENTION_DAYS=90

# WhatsApp Configuration
WHATSAPP_DEVICE_NAME=WhatsApp API Production
WHATSAPP_BROWSER=Chrome
WHATSAPP_VERSION=4.0.0
QR_CODE_TIMEOUT=120000

# Webhook Configuration
WEBHOOK_URL=https://your-webhook-endpoint.com/api/whatsapp-webhook

# Deployment Information
DEPLOYMENT_VERSION=1.0.0
NODE_ID=prod-node-01

# API Configuration
PORT=3000
API_PREFIX=api/v1

# Logging Configuration
LOG_LEVEL=warn

# Performance Tuning
UV_THREADPOOL_SIZE=16
NODE_OPTIONS=--max-old-space-size=32768

# MongoDB Production Optimizations
MONGODB_MAX_POOL_SIZE=20
MONGODB_SERVER_SELECTION_TIMEOUT_MS=10000
MONGODB_SOCKET_TIMEOUT_MS=60000
MONGODB_BUFFER_COMMANDS=false

# Security (set these in your actual environment)
# JWT_SECRET=your-super-secure-jwt-secret
# ENCRYPTION_KEY=your-32-character-encryption-key
# API_KEY=your-api-key-for-authentication
