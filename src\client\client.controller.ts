import {
  <PERSON>,
  Post,
  Param,
  Body,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { WhatsAppService } from '../services/whatsapp.service';
import { SendMessageDto } from '../dto/send-message.dto';

@Controller('client')
export class ClientController {
  private readonly logger = new Logger(ClientController.name);

  constructor(private readonly whatsappService: WhatsAppService) {}

  @Post('sendMessage/:username')
  async sendMessage(
    @Param('username') username: string,
    @Body() sendMessageDto: SendMessageDto,
  ) {
    try {
      const { number, message, document } = sendMessageDto;

      if (!message && !document) {
        throw new HttpException(
          {
            status: HttpStatus.BAD_REQUEST,
            error: 'Bad Request',
            message: 'Either message or document is required',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Format phone number - ensure it has country code
      let formattedNumber = number;
      if (!number.startsWith('+')) {
        // If no plus sign, add it (assuming the number already includes country code)
        formattedNumber = `+${number}`;
      }

      // Remove plus sign for WhatsApp format
      const whatsappNumber = formattedNumber.replace('+', '');

      // 🚀 ENHANCED AUTONOMOUS SEND MESSAGE - Complete session management automation
      return await this.sendMessageWithAutonomousActivation(username, whatsappNumber, message, document);

    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: 'Failed to send message',
          message: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Autonomous message sending with complete session management
   * This method implements the "fire and forget" approach by:
   * 1. Attempting to send message with intelligent session activation
   * 2. If activation fails, calling the same activateSession method as POST /session/activate/:username
   * 3. Retrying the message send after manual activation
   * 4. Only failing if all recovery attempts are exhausted
   */
  private async sendMessageWithAutonomousActivation(
    username: string,
    whatsappNumber: string,
    message?: string,
    document?: string
  ) {
    try {
      // STEP 1: Attempt to send message with intelligent session activation
      this.logger.log(`[${username}] 🚀 AUTONOMOUS SEND: Initial attempt with intelligent activation`);

      const result = await this.whatsappService.sendMessage(
        username,
        whatsappNumber,
        message,
        document,
      );

      this.logger.log(`[${username}] ✅ AUTONOMOUS SEND: Message sent successfully on initial attempt`);
      return {
        success: true,
        data: result,
        message: result.retried ? 'Message sent successfully (after intelligent retry)' : 'Message sent successfully',
        recoveryUsed: false
      };

    } catch (initialError) {
      this.logger.warn(`[${username}] ⚠️ AUTONOMOUS SEND: Initial attempt failed: ${initialError.message}`);

      // Check if this is a session activation failure
      if (this.isSessionActivationFailure(initialError)) {
        this.logger.log(`[${username}] 🔧 AUTONOMOUS SEND: Detected session activation failure - triggering manual activation recovery`);

        try {
          // STEP 2: Call the same activateSession method as POST /session/activate/:username
          this.logger.log(`[${username}] 🔄 AUTONOMOUS SEND: Calling manual session activation (same as POST /session/activate/:username)`);
          await this.whatsappService.activateSession(username);

          this.logger.log(`[${username}] ✅ AUTONOMOUS SEND: Manual session activation completed successfully`);

          // STEP 3: Retry sending the message after manual activation
          this.logger.log(`[${username}] 🔄 AUTONOMOUS SEND: Retrying message send after manual activation`);
          const retryResult = await this.whatsappService.sendMessage(
            username,
            whatsappNumber,
            message,
            document,
          );

          this.logger.log(`[${username}] ✅ AUTONOMOUS SEND: Message sent successfully after manual activation recovery`);
          return {
            success: true,
            data: retryResult,
            message: 'Message sent successfully (after manual activation recovery)',
            recoveryUsed: true,
            recoveryType: 'manual_activation'
          };

        } catch (recoveryError) {
          this.logger.error(`[${username}] ❌ AUTONOMOUS SEND: Manual activation recovery failed: ${recoveryError.message}`);

          // STEP 4: All recovery attempts exhausted - return final error
          throw new Error(`All session recovery attempts failed. Initial error: ${initialError.message}. Recovery error: ${recoveryError.message}`);
        }
      } else {
        // Not a session activation failure - re-throw original error
        this.logger.error(`[${username}] ❌ AUTONOMOUS SEND: Non-activation error - re-throwing: ${initialError.message}`);
        throw initialError;
      }
    }
  }

  /**
   * Determine if an error is related to session activation failure
   */
  private isSessionActivationFailure(error: any): boolean {
    const errorMessage = error.message?.toLowerCase() || '';

    // Check for various session activation failure patterns
    const activationFailurePatterns = [
      'session activation failed',
      'activation failed',
      'connection not established',
      'session not ready',
      'whatsapp session could not be established',
      'session activated but connection not established',
      'unable to establish session',
      'no active socket available',
      'session not found'
    ];

    return activationFailurePatterns.some(pattern => errorMessage.includes(pattern));
  }
}
